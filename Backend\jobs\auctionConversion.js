const Content = require('../models/Content');
const Bid = require('../models/Bid');
const Order = require('../models/Order');
const Notification = require('../models/Notification');
const Setting = require('../models/Setting');
const { getPaymentDeadline } = require('../config/timeouts');
const { generateBidAcceptanceEmail } = require('../utils/emailTemplates');
const sendEmail = require('../utils/sendEmail');

class AuctionConversionJob {
    constructor() {
        this.jobName = 'auctionConversion';
    }

    async run() {
        console.log('🔄 Starting auction conversion job...');

        try {
            // Use UTC date for comparison
            const currentTime = new Date();

            // Find all auction content that has ended but hasn't been processed
            const expiredAuctions = await Content.find({
                saleType: { $in: ['Auction'] },
                'auctionDetails.auctionEndDate': {
                    $lt: currentTime // MongoDB automatically handles UTC conversion
                },
                auctionStatus: { $ne: 'Ended' },
                isSold: false,
                status: 'Published'
            }).populate('seller', 'firstName lastName email');

            console.log(`📊 Found ${expiredAuctions.length} expired auctions to check`);

            let conversionsCount = 0;
            let autoWinsCount = 0;
            const results = {
                conversions: [],
                autoWins: [],
                errors: []
            };

            for (const auction of expiredAuctions) {
                try {
                    // Get all active bids for this auction, sorted by amount (highest first)
                    const activeBids = await Bid.find({
                        content: auction._id,
                        status: 'Active'
                    }).populate('bidder', 'firstName lastName email').sort('-amount');

                    if (activeBids.length === 0) {
                        // No active bids - convert to Fixed Price
                        await this.convertToFixedPrice(auction, currentTime, results);
                        conversionsCount++;
                    } else {
                        // Has bids - automatically select highest bidder as winner
                        await this.autoSelectWinner(auction, activeBids, currentTime, results);
                        autoWinsCount++;
                    }
                } catch (error) {
                    console.error(`❌ Error processing auction "${auction.title}":`, error);
                    results.errors.push({
                        auctionId: auction._id,
                        title: auction.title,
                        error: error.message
                    });
                }
            }

            const result = {
                totalChecked: expiredAuctions.length,
                conversions: conversionsCount,
                autoWins: autoWinsCount,
                errors: results.errors.length,
                details: results,
                timestamp: currentTime.toISOString()
            };

            // Log detailed summary
            if (results.errors.length > 0) {
                console.log(`⚠️ Auction conversion job completed with ${results.errors.length} errors:`);
                results.errors.forEach(error => {
                    console.log(`   - ${error.title}: ${error.error}`);
                });
            }

            console.log(`✅ Auction conversion job completed: ${conversionsCount} conversions, ${autoWinsCount} auto-wins from ${expiredAuctions.length} expired auctions`);

            // Log audit trail for important actions
            if (autoWinsCount > 0) {
                console.log(`🏆 Auto-wins processed:`);
                results.autoWins.forEach(win => {
                    console.log(`   - "${win.title}" won by ${win.winnerEmail} for $${win.amount} (Order: ${win.orderId})`);
                });
            }

            if (conversionsCount > 0) {
                console.log(`🔄 Conversions processed:`);
                results.conversions.forEach(conversion => {
                    console.log(`   - "${conversion.title}" converted to fixed price: $${conversion.conversionPrice}`);
                });
            }

            return result;

        } catch (error) {
            console.error('❌ Auction conversion job failed:', error);
            throw error;
        }
    }

    /**
     * Convert auction with no bids to fixed-price sale
     */
    async convertToFixedPrice(auction, currentTime, results) {
        const conversionPrice = auction.auctionDetails.basePrice || auction.price || 0;
        const utcCurrentTime = new Date(currentTime.toISOString());

        await Content.findByIdAndUpdate(auction._id, {
            saleType: 'Fixed',
            price: conversionPrice,
            auctionStatus: 'Ended',
            auctionEndedAt: utcCurrentTime,
            'auctionDetails.endTime': utcCurrentTime
        });

        // Send notification to seller about conversion
        await this.sendConversionNotification(auction, conversionPrice);

        console.log(`✅ Converted auction "${auction.title}" to Fixed Price ($${conversionPrice})`);

        results.conversions.push({
            auctionId: auction._id,
            title: auction.title,
            conversionPrice: conversionPrice,
            timestamp: utcCurrentTime.toISOString()
        });
    }

    /**
     * Automatically select highest bidder as winner
     */
    async autoSelectWinner(auction, activeBids, currentTime, results) {
        const highestBid = activeBids[0]; // Already sorted by amount descending

        // Check reserve price if set
        if (auction.auctionDetails.reservePrice &&
            highestBid.amount < auction.auctionDetails.reservePrice) {

            // Reserve price not met - update all bids to Lost and convert to fixed price
            await Bid.updateMany(
                { content: auction._id, status: 'Active' },
                { status: 'Lost' }
            );

            await this.convertToFixedPrice(auction, currentTime, results);

            console.log(`⚠️ Auction "${auction.title}" reserve price not met ($${highestBid.amount} < $${auction.auctionDetails.reservePrice}), converted to fixed price`);
            return;
        }

        // Reserve price met or no reserve price - proceed with auto-win
        await this.processAutoWin(auction, highestBid, currentTime, results);
    }

    /**
     * Process automatic winner selection and create order
     */
    async processAutoWin(auction, winningBid, currentTime, results) {
        try {
            // Update winning bid status
            winningBid.status = 'Won';
            await winningBid.save();

            // Update all other bids to Lost
            await Bid.updateMany(
                { content: auction._id, status: 'Active', _id: { $ne: winningBid._id } },
                { status: 'Lost' }
            );

            // Get settings for fee calculation
            const settings = await Setting.getSingleton();
            const feeBreakdown = Setting.calculateFeeBreakdown(winningBid.amount, settings);

            // Create order for the winning bid
            const order = await Order.create({
                buyer: winningBid.bidder._id,
                seller: auction.seller._id,
                content: auction._id,
                orderType: "Auction",
                amount: winningBid.amount,
                platformFee: feeBreakdown.platformCommission,
                sellerEarnings: feeBreakdown.finalSellerEarnings,
                totalAmount: winningBid.amount,
                bidId: winningBid._id,
                paymentDeadline: getPaymentDeadline(),
                status: "Pending",
            });

            // Update auction status and mark as sold (ensure UTC format)
            const utcCurrentTime = new Date(currentTime.toISOString());
            await Content.findByIdAndUpdate(auction._id, {
                auctionStatus: 'Ended',
                auctionEndedAt: utcCurrentTime,
                'auctionDetails.endTime': utcCurrentTime,
                isSold: true,
                soldAt: utcCurrentTime,
                winningBidId: winningBid._id
            });

            // Send notifications
            await this.sendWinnerNotification(auction, winningBid, order);
            await this.sendSellerAutoWinNotification(auction, winningBid);

            console.log(`🏆 Auto-selected winner for auction "${auction.title}": ${winningBid.bidder.firstName} ${winningBid.bidder.lastName} ($${winningBid.amount})`);

            results.autoWins.push({
                auctionId: auction._id,
                title: auction.title,
                winningBidId: winningBid._id,
                winnerEmail: winningBid.bidder.email,
                amount: winningBid.amount,
                orderId: order._id,
                timestamp: utcCurrentTime.toISOString()
            });

        } catch (error) {
            console.error(`❌ Error processing auto-win for auction "${auction.title}":`, error);
            throw error;
        }
    }

    /**
     * Send notification to auction winner using existing bid acceptance email template
     */
    async sendWinnerNotification(auction, winningBid, order) {
        try {
            const checkoutUrl = `${process.env.FRONTEND_URL}/checkout/${order._id}`;

            // Create in-app notification
            await Notification.create({
                user: winningBid.bidder._id,
                title: 'Congratulations! You Won the Auction',
                message: `Your bid of $${winningBid.amount} for "${auction.title}" was automatically selected as the winning bid. Complete your payment to access the content.`,
                type: 'bid',
                relatedId: winningBid._id,
                onModel: 'Bid'
            });

            // Send email notification using existing bid acceptance template
            const emailData = generateBidAcceptanceEmail({
                bidder: winningBid.bidder,
                content: {
                    title: auction.title,
                    sport: auction.sport,
                    contentType: auction.contentType,
                    seller: auction.seller,
                },
                bid: winningBid,
                checkoutUrl: checkoutUrl,
            });

            await sendEmail({
                to: winningBid.bidder.email,
                subject: emailData.subject,
                message: emailData.message,
                html: emailData.html,
            });

            console.log(`📧 Winner notification sent to ${winningBid.bidder.email}`);

        } catch (error) {
            console.error('❌ Error sending winner notification:', error);
            // Don't throw - notification failure shouldn't stop the auction resolution
        }
    }

    /**
     * Send notification to seller about auto-win
     */
    async sendSellerAutoWinNotification(auction, winningBid) {
        try {
            // Create in-app notification
            await Notification.create({
                user: auction.seller._id,
                title: 'Auction Automatically Resolved',
                message: `Your auction "${auction.title}" has been automatically resolved. The highest bidder ($${winningBid.amount}) has been selected as the winner.`,
                type: 'account',
                relatedId: auction._id,
                onModel: 'Content'
            });

            console.log(`📧 Seller auto-win notification sent for auction "${auction.title}"`);

        } catch (error) {
            console.error('❌ Error sending seller auto-win notification:', error);
            // Don't throw - notification failure shouldn't stop the auction resolution
        }
    }

    /**
     * Send notification to seller about auction conversion to fixed price
     */
    async sendConversionNotification(auction, conversionPrice) {
        try {
            // Create in-app notification only (no separate email for conversion)
            await Notification.create({
                user: auction.seller._id,
                title: 'Auction Converted to Fixed Price',
                message: `Your auction "${auction.title}" received no bids and has been automatically converted to a fixed-price sale at $${conversionPrice}.`,
                type: 'account',
                relatedId: auction._id,
                onModel: 'Content'
            });

            console.log(`📧 Conversion notification created for auction "${auction.title}"`);

        } catch (error) {
            console.error('❌ Error sending conversion notification:', error);
            // Don't throw - notification failure shouldn't stop the auction conversion
        }
    }
}

module.exports = new AuctionConversionJob();